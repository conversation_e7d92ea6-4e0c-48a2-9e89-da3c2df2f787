<template>
  <div
    v-if="visible"
    class="scene-payment-modal-overlay"
    @click="handleOverlayClick"
  >
    <div class="scene-payment-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3 class="modal-title">Scene Access</h3>
        <button class="close-button" @click="handleClose">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M15 5L5 15M5 5L15 15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="modal-content">
        <div class="scene-info">
          <h4 class="scene-name">{{ sceneName }}</h4>
          <p class="payment-message">
            This scene requires
            <span class="coins-amount">
              <img
                src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                class="inline-diamond"
              />
              {{ coinsRequired }}
            </span>
            to access.
          </p>
        </div>

        <div class="coins-display">
          <div class="current-coins">
            <span class="label">Your balance:</span>
            <span class="amount">
              <img
                src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                class="inline-diamond"
              />
              {{ currentCoins }}
            </span>
          </div>
          <div class="after-payment" v-if="currentCoins >= coinsRequired">
            <span class="label">After payment:</span>
            <span class="amount">
              <img
                src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                class="inline-diamond"
              />
              {{ currentCoins - coinsRequired }}
            </span>
          </div>
        </div>

        <!-- 余额不足提示 -->
        <div v-if="currentCoins < coinsRequired" class="insufficient-warning">
          <div class="warning-icon">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"
                fill="#ff6b6b"
              />
            </svg>
          </div>
          <p>You need a few more diamonds to unlock this special moment! ✨</p>
        </div>
      </div>

      <!-- 按钮 -->
      <div class="modal-actions">
        <button class="cancel-button" @click="handleCancel"> Cancel </button>
        <button
          class="confirm-button"
          :disabled="currentCoins < coinsRequired"
          @click="handleConfirm"
        >
          <span v-if="currentCoins >= coinsRequired">
            Pay
            <img
              src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              class="button-diamond"
            />{{ coinsRequired }}
          </span>
          <span v-else>Get Diamonds</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  sceneName: string
  coinsRequired: number
  currentCoins: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  sceneName: '',
  coinsRequired: 0,
  currentCoins: 0,
})

const emit = defineEmits<{
  close: []
  confirm: []
  cancel: []
}>()

const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  if (props.currentCoins >= props.coinsRequired) {
    emit('confirm')
  } else {
    // 余额不足，可以触发充值
    emit('cancel')
  }
}

const handleOverlayClick = () => {
  emit('close')
}
</script>

<style lang="less" scoped>
.scene-payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.scene-payment-modal {
  background: linear-gradient(145deg, #2a1f3d 0%, #1a1225 100%);
  border-radius: 20px;
  border: 1px solid rgba(218, 255, 150, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(218, 255, 150, 0.1);
}

.scene-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  .diamond-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}

.modal-title {
  flex: 1;
  font-family: 'Work Sans', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
}

.modal-content {
  padding: 24px;
}

.scene-info {
  text-align: center;
  margin-bottom: 24px;
}

.scene-name {
  font-family: 'Work Sans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #daff96;
  margin: 0 0 12px 0;
}

.payment-message {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

.coins-amount {
  font-weight: 700;
  color: #daff96;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.inline-diamond {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.button-diamond {
  width: 12px;
  height: 12px;
  object-fit: contain;
  margin: 0 2px;
}

.coins-display {
  background: rgba(218, 255, 150, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.current-coins,
.after-payment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.amount {
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #daff96;
  display: flex;
  align-items: center;
  gap: 4px;
}

.insufficient-warning {
  display: flex;
  align-items: center;
  background: rgba(255, 77, 77, 0.1);
  border: 1px solid rgba(255, 77, 77, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 20px;

  .warning-icon {
    margin-right: 12px;
    font-size: 18px;
  }

  p {
    margin: 0;
    font-family: 'Work Sans', sans-serif;
    font-size: 14px;
    color: #ff6b6b;
    font-weight: 500;
  }
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 0 24px 24px;
}

.cancel-button,
.confirm-button {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-button {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
  }
}

.confirm-button {
  background: linear-gradient(135deg, #daff96 0%, #b8e66d 100%);
  color: #1a1225;
  box-shadow: 0 4px 12px rgba(218, 255, 150, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(218, 255, 150, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .scene-payment-modal {
    width: 95%;
    margin: 0 auto;
  }

  .modal-header {
    padding: 16px 20px 12px;
  }

  .modal-title {
    font-size: 18px;
  }

  .modal-content {
    padding: 20px;
  }

  .modal-actions {
    padding: 0 20px 20px;
    gap: 8px;
  }

  .cancel-button,
  .confirm-button {
    height: 44px;
    font-size: 15px;
  }
}
</style>
